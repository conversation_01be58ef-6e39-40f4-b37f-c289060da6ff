// Background.js Part 2 - Starting from line 4067
// This file contains the remaining JavaScript logic and CSS from the original background.js

// Continue from background.1.js - this should be appended to the h object
},IMMERSIVE_TRANSLATE_POPUP_CSS:`body {
  padding: 0;
  margin: 0 auto;
  min-width: 268px;
  border-radius: 10px;
}

.popup-container {
  font-size: 16px;
  --font-size: 16px;
  color: #666;
  background-color: var(--popup-footer-background-color);
  width: 316px;
  min-width: 316px;
}

.popup-content {
  background-color: var(--popup-content-background-color);
  border-radius: 0px 0px 12px 12px;
  padding: 16px 20px;
}

.immersive-translate-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  touch-action: none;
}

.immersive-translate-popup-wrapper {
  background: var(--background-color);
  border-radius: 10px;
  border: 1px solid var(--muted-border-color);
}

.immersive-translate-popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px 0px 20px;
  background-color: var(--popup-header-background-color);
  border-radius: 12px 12px 0px 0px;
}

.immersive-translate-popup-header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.immersive-translate-popup-header-text {
  font-size: 18px;
  font-weight: 600;
}

.immersive-translate-popup-header-button {
  background: none;
  border: none;
  cursor: pointer;
}

.immersive-translate-popup-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background-color: var(--popup-footer-background-color);
  border-radius: 0px 0px 12px 12px;
}

.immersive-translate-popup-footer-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.immersive-translate-popup-footer-text {
  font-size: 14px;
  color: var(--muted-color);
}

.immersive-translate-popup-footer-button {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--primary);
  font-size: 14px;
}

.immersive-translate-popup-footer-button:hover {
  color: var(--primary-hover);
}

.immersive-translate-popup-main {
  padding: 16px 20px;
}

.immersive-translate-popup-main-text {
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 16px;
}

.immersive-translate-popup-main-language {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.immersive-translate-popup-main-language-text {
  font-size: 14px;
  color: var(--muted-color);
}

.immersive-translate-popup-main-language-select {
  background: var(--background-color);
  border: 1px solid var(--muted-border-color);
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  color: var(--color);
}

.immersive-translate-popup-main-buttons {
  display: flex;
  gap: 8px;
}

.immersive-translate-popup-main-button {
  flex: 1;
  padding: 8px 16px;
  border: 1px solid var(--primary);
  border-radius: 4px;
  background: var(--primary);
  color: var(--primary-inverse);
  font-size: 14px;
  cursor: pointer;
}

.immersive-translate-popup-main-button:hover {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}

.immersive-translate-popup-main-button.secondary {
  background: transparent;
  color: var(--primary);
}

.immersive-translate-popup-main-button.secondary:hover {
  background: var(--primary-focus);
}

.immersive-translate-popup-close {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  color: var(--muted-color);
}

.immersive-translate-popup-close:hover {
  color: var(--color);
}

.immersive-translate-popup-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
}

.immersive-translate-popup-arrow.top {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 0 8px 8px 8px;
  border-color: transparent transparent var(--muted-border-color) transparent;
}

.immersive-translate-popup-arrow.bottom {
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 8px 8px 0 8px;
  border-color: var(--muted-border-color) transparent transparent transparent;
}

.immersive-translate-popup-arrow.left {
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-width: 8px 8px 8px 0;
  border-color: transparent var(--muted-border-color) transparent transparent;
}

.immersive-translate-popup-arrow.right {
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-width: 8px 0 8px 8px;
  border-color: transparent transparent transparent var(--muted-border-color);
}

@media (max-width: 768px) {
  .popup-container {
    width: 100%;
    min-width: 100%;
  }
  
  .immersive-translate-popup-wrapper {
    margin: 16px;
    max-width: calc(100% - 32px);
  }
}
`,IMMERSIVE_TRANSLATE_PAGE_POPUP_CSS:`#mount {
  --font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  --line-height: 1.5;
  --font-weight: 400;
  --font-size: 16px;
  --border-radius: 0.25rem;
  --border-width: 1px;
  --outline-width: 3px;
  --spacing: 1rem;
  --typography-spacing-vertical: 1.5rem;
  --block-spacing-vertical: calc(var(--spacing) * 2);
  --block-spacing-horizontal: var(--spacing);
  --grid-spacing-vertical: 0;
  --grid-spacing-horizontal: var(--spacing);
  --form-element-spacing-vertical: 0.75rem;
  --form-element-spacing-horizontal: 1rem;
  --nav-element-spacing-vertical: 1rem;
  --nav-element-spacing-horizontal: 0.5rem;
  --nav-link-spacing-vertical: 0.5rem;
  --nav-link-spacing-horizontal: 0.5rem;
  --form-label-font-weight: var(--font-weight);
  --transition: 0.2s ease-in-out;
  --modal-overlay-backdrop-filter: blur(0.25rem);
}

.immersive-translate-page-popup {
  position: fixed;
  z-index: 2147483647;
  background: var(--background-color);
  border: 1px solid var(--muted-border-color);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-family: var(--font-family);
  font-size: var(--font-size);
  line-height: var(--line-height);
  color: var(--color);
  max-width: 400px;
  min-width: 300px;
}

.immersive-translate-page-popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--muted-border-color);
  background: var(--background-color);
  border-radius: 8px 8px 0 0;
}

.immersive-translate-page-popup-title {
  font-weight: 600;
  font-size: 16px;
  margin: 0;
}

.immersive-translate-page-popup-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  color: var(--muted-color);
  border-radius: 4px;
}

.immersive-translate-page-popup-close:hover {
  background: var(--muted-border-color);
  color: var(--color);
}

.immersive-translate-page-popup-content {
  padding: 16px;
}

.immersive-translate-page-popup-content p {
  margin: 0 0 12px 0;
  line-height: 1.5;
}

.immersive-translate-page-popup-content p:last-child {
  margin-bottom: 0;
}

.immersive-translate-page-popup-buttons {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

.immersive-translate-page-popup-button {
  flex: 1;
  padding: 8px 16px;
  border: 1px solid var(--primary);
  border-radius: 4px;
  background: var(--primary);
  color: var(--primary-inverse);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.immersive-translate-page-popup-button:hover {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}

.immersive-translate-page-popup-button.secondary {
  background: transparent;
  color: var(--primary);
}

.immersive-translate-page-popup-button.secondary:hover {
  background: var(--primary-focus);
}

@media (max-width: 768px) {
  .immersive-translate-page-popup {
    max-width: calc(100vw - 32px);
    min-width: calc(100vw - 32px);
  }
}
`,IMMERSIVE_TRANSLATE_COMMON_CSS:`.immersive-translate-target-wrapper {
  color: rgb(0, 0, 0);
  white-space: normal;
  position: absolute;
}

.immersive-translate-target-wrapper font {
  color: inherit;
  white-space: inherit;
  position: unset;
}

.immersive-translate-target-translation-pre-whitespace {
  white-space: pre-wrap !important;
}

.immersive-translate-target-translation-block-wrapper {
  margin: 8px 0 !important;
  display: inline-block;
}

.immersive-translate-target-translation-pdf-block-wrapper {
  margin: 0 !important;
  display: inline-block;
}

.immersive-translate-target-translation-theme-grey-inner {
  color: var(--immersive-translate-theme-grey-textColor);
}

.immersive-translate-target-translation-theme-underline-inner {
  border-bottom: 1px solid var(--immersive-translate-theme-underline-borderColor) !important;
}

.immersive-translate-target-translation-theme-nativeUnderline-inner {
  text-decoration: underline !important;
  text-decoration-color: var(--immersive-translate-theme-nativeUnderline-borderColor) !important;
}

.immersive-translate-target-translation-block-wrapper-theme-dashedBorder {
  border: 1px dashed var(--immersive-translate-theme-dashedBorder-borderColor) !important;
  border-radius: var(--immersive-translate-theme-dashedBorder-borderRadius) !important;
  padding: 6px;
  margin-top: 2px;
  display: inline-block;
}

.immersive-translate-target-translation-inline-wrapper-theme-dashedBorder {
  border: 1px dashed var(--immersive-translate-theme-dashedBorder-borderColor) !important;
  border-radius: var(--immersive-translate-theme-dashedBorder-borderRadius) !important;
  padding: 2px;
}

.immersive-translate-target-translation-block-wrapper-theme-solidBorder {
  border: 1px solid var(--immersive-translate-theme-solidBorder-borderColor) !important;
  border-radius: var(--immersive-translate-theme-solidBorder-borderRadius) !important;
  padding: 6px;
  margin-top: 2px;
  display: inline-block;
}

.immersive-translate-target-translation-inline-wrapper-theme-solidBorder {
  border: 1px solid var(--immersive-translate-theme-solidBorder-borderColor) !important;
  border-radius: var(--immersive-translate-theme-solidBorder-borderRadius) !important;
  padding: 2px;
}

.immersive-translate-target-translation-theme-nativeDashed-inner {
  text-decoration: underline !important;
  text-decoration-color: var(--immersive-translate-theme-nativeDashed-borderColor) !important;
  text-decoration-style: dashed !important;
}

.immersive-translate-target-translation-theme-thinDashed-inner {
  border-bottom: 1px dashed var(--immersive-translate-theme-thinDashed-borderColor) !important;
}

.immersive-translate-target-translation-theme-dotted-inner {
  background-image: linear-gradient(
    to right,
    var(--immersive-translate-theme-dotted-borderColor) 30%,
    rgba(255, 255, 255, 0) 0%
  );
  background-position: bottom;
  background-size: 5px 1px;
  background-repeat: repeat-x;
  padding-bottom: 3px;
}

.immersive-translate-target-translation-theme-nativeDotted-inner {
  text-decoration: underline !important;
  text-decoration-color: var(--immersive-translate-theme-nativeDotted-borderColor) !important;
  text-decoration-style: dotted !important;
}

.immersive-translate-target-translation-theme-wavy-inner {
  text-decoration: underline !important;
  text-decoration-color: var(--immersive-translate-theme-wavy-borderColor) !important;
  text-decoration-style: wavy !important;
}

.immersive-translate-target-translation-theme-dashed-inner {
  background: linear-gradient(
      to right,
      var(--immersive-translate-theme-dashed-borderColor) 0%,
      var(--immersive-translate-theme-dashed-borderColor) 50%,
      transparent 50%,
      transparent 100%
    )
    repeat-x left bottom;
  background-size: 8px 2px;
  padding-bottom: 2px;
}

.immersive-translate-target-translation-block-wrapper-theme-dividingLine::before {
  content: "";
  display: block;
  max-width: 80px;
  width: 10%;
  border-top: 1px dashed var(--immersive-translate-theme-dividingLine-borderColor);
  padding-top: 8px;
}

.immersive-translate-target-translation-inline-wrapper-theme-dividingLine::before {
  content: "";
  border-left: 1px dashed var(--immersive-translate-theme-dividingLine-borderColor);
  max-height: 16px;
  height: 16px;
  padding-left: 8px;
}

.immersive-translate-target-translation-theme-highlight-inner {
  background: var(--immersive-translate-theme-highlight-backgroundColor);
  box-decoration-break: clone;
  -webkit-box-decoration-break: clone;
}

.immersive-translate-target-translation-block-wrapper-theme-marker {
  line-height: 1.5em;
}

.immersive-translate-target-translation-theme-marker2-inner {
  font-weight: bold;
  text-shadow: 10px 0px 3px var(--immersive-translate-theme-marker2-backgroundColor),
    16px 3px 9px var(--immersive-translate-theme-marker2-backgroundColor),
    2px 0px 6px var(--immersive-translate-theme-marker2-backgroundColor),
    -12px 0px 12px var(--immersive-translate-theme-marker2-backgroundColor) !important;
}

.immersive-translate-target-translation-theme-marker-inner {
  background: linear-gradient(
    to right,
    rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.1),
    rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.9) 3%,
    rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.9) 35%,
    rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.9) 70%,
    rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.8) 95%,
    rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.3)
  );
  box-decoration-break: clone;
  -webkit-box-decoration-break: clone;
}

.immersive-translate-target-translation-theme-weakening {
  opacity: 0.618 !important;
}

.immersive-translate-target-translation-theme-italic {
  font-style: italic !important;
}

.immersive-translate-target-translation-theme-bold {
  font-weight: bold !important;
}

.immersive-translate-target-translation-block-wrapper-theme-paper {
  margin: 8px 0;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  padding: 16px 32px;
  display: inline-block;
}

.immersive-translate-target-translation-block-wrapper-theme-blockquote {
  border-left: 4px solid var(--immersive-translate-theme-blockquote-borderColor) !important;
  padding-left: 12px !important;
  margin-top: 4px;
  margin-bottom: 4px;
  padding-top: 4px;
  padding-bottom: 4px;
  display: inline-block;
}

.immersive-translate-target-translation-theme-mask-inner {
  filter: blur(5px) !important;
  transition: filter 0.3s ease !important;
  border-radius: 10px;
  display: inline-block;
}

.immersive-translate-target-translation-theme-mask-inner:hover {
  filter: none !important;
}
`
