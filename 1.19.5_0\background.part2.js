// Background.js Part 2 - Starting from line 4067
// This file contains the remaining CSS and JavaScript logic from the original background.js
// This should be concatenated with background.1.js to form the complete background.js

// Continue the h object from background.1.js
,IMMERSIVE_TRANSLATE_POPUP_CSS:`body {
  padding: 0;
  margin: 0 auto;
  min-width: 268px;
  border-radius: 10px;
}

.popup-container {
  font-size: 16px;
  --font-size: 16px;
  color: #666;
  background-color: var(--popup-footer-background-color);
  width: 316px;
  min-width: 316px;
}

.popup-content {
  background-color: var(--popup-content-background-color);
  border-radius: 0px 0px 12px 12px;
  padding: 16px 20px;
}

.immersive-translate-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  touch-action: none;
}

.immersive-translate-popup-wrapper {
  background: var(--background-color);
  border-radius: 10px;
  border: 1px solid var(--muted-border-color);
}

#mount#mount {
  --font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  --line-height: 1.5;
  --font-weight: 400;
  --font-size: 16px;
  --border-radius: 4px;
  --border-width: 1px;
  --outline-width: 3px;
  --spacing: 16px;
  --typography-spacing-vertical: 24px;
  --block-spacing-vertical: calc(var(--spacing) * 2);
  --block-spacing-horizontal: var(--spacing);
  --grid-spacing-vertical: 0;
  --grid-spacing-horizontal: var(--spacing);
  --form-element-spacing-vertical: 12px;
  --form-element-spacing-horizontal: 16px;
  --nav-element-spacing-vertical: 16px;
  --nav-element-spacing-horizontal: 8px;
  --nav-link-spacing-vertical: 8px;
  --nav-link-spacing-horizontal: 8px;
  --form-label-font-weight: var(--font-weight);
  --transition: 0.2s ease-in-out;
  --modal-overlay-backdrop-filter: blur(4px);
}

[data-theme="light"],
#mount:not([data-theme="dark"]) {
  --popup-footer-background-color: #e8eaeb;
  --popup-content-background-color: #ffffff;
  --popup-item-background-color: #f3f5f6;
  --popup-item-hover-background-color: #eaeced;
  --popup-trial-pro-background-color: #f9fbfc;
  --text-black-2: #222222;
  --text-gray-2: #222222;
  --text-gray-6: #666666;
  --text-gray-9: #999999;
  --text-gray-c2: #c2c2c2;
  --service-select-content-shadow: 0px 2px 12px 0px rgba(75, 76, 77, 0.2);
  --service-select-border-color: #fafafa;
  --service-select-selected-background-color: #f3f5f6;
}

@media only screen and (prefers-color-scheme: dark) {
  #mount:not([data-theme="light"]) {
    --popup-footer-background-color: #0d0d0d;
    --popup-content-background-color: #191919;
    --popup-item-background-color: #272727;
    --popup-item-hover-background-color: #333333;
    --popup-trial-pro-background-color: #222222;
    --text-black-2: #ffffff;
    --text-gray-2: #dbdbdb;
    --text-gray-6: #b3b3b3;
    --text-gray-9: #777777;
    --text-gray-c2: #5b5b5b;
    --service-select-content-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.9);
    --service-select-border-color: #2c2c2c;
    --service-select-selected-background-color: #333333;
  }
}

[data-theme="dark"] {
  --popup-footer-background-color: #0d0d0d;
  --popup-content-background-color: #191919;
  --popup-item-background-color: #272727;
  --popup-item-hover-background-color: #333333;
  --popup-trial-pro-background-color: #222222;
  --text-black-2: #ffffff;
  --text-gray-2: #dbdbdb;
  --text-gray-6: #b3b3b3;
  --text-gray-9: #777777;
  --text-gray-c2: #5b5b5b;
  --service-select-content-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.9);
  --service-select-border-color: #2c2c2c;
  --service-select-selected-background-color: #333333;
}

.text-balck {
  color: var(--text-black-2);
}

.text-gray-2 {
  color: var(--text-gray-2);
}

.text-gray-6 {
  color: var(--text-gray-6);
}

.text-gray-9 {
  color: var(--text-gray-9);
}

.text-gray-c2 {
  color: var(--text-gray-c2);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-1 {
  gap: 4px;
}

.gap-2 {
  gap: 8px;
}

.gap-3 {
  gap: 12px;
}

.gap-4 {
  gap: 16px;
}

.p-1 {
  padding: 4px;
}

.p-2 {
  padding: 8px;
}

.p-3 {
  padding: 12px;
}

.p-4 {
  padding: 16px;
}

.px-1 {
  padding-left: 4px;
  padding-right: 4px;
}

.px-2 {
  padding-left: 8px;
  padding-right: 8px;
}

.px-3 {
  padding-left: 12px;
  padding-right: 12px;
}

.py-1 {
  padding-top: 4px;
  padding-bottom: 4px;
}

.py-2 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.py-3 {
  padding-top: 12px;
  padding-bottom: 12px;
}

.m-1 {
  margin: 4px;
}

.m-2 {
  margin: 8px;
}

.m-3 {
  margin: 12px;
}

.mx-1 {
  margin-left: 4px;
  margin-right: 4px;
}

.mx-2 {
  margin-left: 8px;
  margin-right: 8px;
}

.mx-3 {
  margin-left: 12px;
  margin-right: 12px;
}

.my-1 {
  margin-top: 4px;
  margin-bottom: 4px;
}

.my-2 {
  margin-top: 8px;
  margin-bottom: 8px;
}

.my-3 {
  margin-top: 16px;
  margin-bottom: 16px;
}

.pt-3 {
  padding-top: 12px;
}

.pt-2 {
  padding-top: 8px;
}

.pt-1 {
  padding-top: 4px;
}

.pb-2 {
  padding-bottom: 8px;
}

.justify-end {
  justify-content: flex-end;
}

.w-auto {
  width: auto;
}

.shrink-0 {
  flex-shrink: 0;
}

select.language-select,
select.translate-service,
select.min-select {
  --form-element-spacing-horizontal: 0;
  margin-bottom: 0px;
  max-width: unset;
  flex: 1;
  overflow: hidden;
  font-size: 13px;
  border: none;
  border-radius: 8px;
  padding-right: 30px;
  padding-left: 0px;
  background-position: center right 12px;
  background-size: 16px auto;
  background-image: var(--icon-xia);
  text-overflow: ellipsis;
  color: var(--text-gray-2);
  background-color: transparent;
  box-shadow: unset !important;
  cursor: pointer;
}

select.more {
  background-position: center right;
  padding-right: 20px;
}

select.transform-padding-left {
  padding-left: 12px;
  transform: translateX(-12px);
  background-position: center right 0px;
}

select.translate-service {
  color: var(--text-black-2);
}

/* dark use black, for windows */
@media (prefers-color-scheme: dark) {
  select.language-select option,
  select.translate-service option,
  select.min-select option {
    background-color: #666666;
  }
}

.text-overflow-ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.max-w-20 {
  max-width: 180px;
  white-space: nowrap;
}
`
