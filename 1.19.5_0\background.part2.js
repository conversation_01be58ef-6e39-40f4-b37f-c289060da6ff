// Background.js Part 2 - Starting from line 4067
// This file contains the remaining CSS and JavaScript logic from the original background.js
// This should be concatenated with background.1.js to form the complete background.js

// Continue the h object from background.1.js
,IMMERSIVE_TRANSLATE_POPUP_CSS:`body {
  padding: 0;
  margin: 0 auto;
  min-width: 268px;
  border-radius: 10px;
}

.popup-container {
  font-size: 16px;
  --font-size: 16px;
  color: #666;
  background-color: var(--popup-footer-background-color);
  width: 316px;
  min-width: 316px;
}

.popup-content {
  background-color: var(--popup-content-background-color);
  border-radius: 0px 0px 12px 12px;
  padding: 16px 20px;
}

.immersive-translate-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  touch-action: none;
}

.immersive-translate-popup-wrapper {
  background: var(--background-color);
  border-radius: 10px;
  border: 1px solid var(--muted-border-color);
}

#mount#mount {
  --font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  --line-height: 1.5;
  --font-weight: 400;
  --font-size: 16px;
  --border-radius: 4px;
  --border-width: 1px;
  --outline-width: 3px;
  --spacing: 16px;
  --typography-spacing-vertical: 24px;
  --block-spacing-vertical: calc(var(--spacing) * 2);
  --block-spacing-horizontal: var(--spacing);
  --grid-spacing-vertical: 0;
  --grid-spacing-horizontal: var(--spacing);
  --form-element-spacing-vertical: 12px;
  --form-element-spacing-horizontal: 16px;
  --nav-element-spacing-vertical: 16px;
  --nav-element-spacing-horizontal: 8px;
  --nav-link-spacing-vertical: 8px;
  --nav-link-spacing-horizontal: 8px;
  --form-label-font-weight: var(--font-weight);
  --transition: 0.2s ease-in-out;
  --modal-overlay-backdrop-filter: blur(4px);
}

[data-theme="light"],
#mount:not([data-theme="dark"]) {
  --popup-footer-background-color: #e8eaeb;
  --popup-content-background-color: #ffffff;
  --popup-item-background-color: #f3f5f6;
  --popup-item-hover-background-color: #eaeced;
  --popup-trial-pro-background-color: #f9fbfc;
  --text-black-2: #222222;
  --text-gray-2: #222222;
  --text-gray-6: #666666;
  --text-gray-9: #999999;
  --text-gray-c2: #c2c2c2;
  --service-select-content-shadow: 0px 2px 12px 0px rgba(75, 76, 77, 0.2);
  --service-select-border-color: #fafafa;
  --service-select-selected-background-color: #f3f5f6;
}

@media only screen and (prefers-color-scheme: dark) {
  #mount:not([data-theme="light"]) {
    --popup-footer-background-color: #0d0d0d;
    --popup-content-background-color: #191919;
    --popup-item-background-color: #272727;
    --popup-item-hover-background-color: #333333;
    --popup-trial-pro-background-color: #222222;
    --text-black-2: #ffffff;
    --text-gray-2: #dbdbdb;
    --text-gray-6: #b3b3b3;
    --text-gray-9: #777777;
    --text-gray-c2: #5b5b5b;
    --service-select-content-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.9);
    --service-select-border-color: #2c2c2c;
    --service-select-selected-background-color: #333333;
  }
}

[data-theme="dark"] {
  --popup-footer-background-color: #0d0d0d;
  --popup-content-background-color: #191919;
  --popup-item-background-color: #272727;
  --popup-item-hover-background-color: #333333;
  --popup-trial-pro-background-color: #222222;
  --text-black-2: #ffffff;
  --text-gray-2: #dbdbdb;
  --text-gray-6: #b3b3b3;
  --text-gray-9: #777777;
  --text-gray-c2: #5b5b5b;
  --service-select-content-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.9);
  --service-select-border-color: #2c2c2c;
  --service-select-selected-background-color: #333333;
}

.text-balck {
  color: var(--text-black-2);
}

.text-gray-2 {
  color: var(--text-gray-2);
}

.text-gray-6 {
  color: var(--text-gray-6);
}

.text-gray-9 {
  color: var(--text-gray-9);
}

.text-gray-c2 {
  color: var(--text-gray-c2);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-1 {
  gap: 4px;
}

.gap-2 {
  gap: 8px;
}

.gap-3 {
  gap: 12px;
}

.gap-4 {
  gap: 16px;
}

.p-1 {
  padding: 4px;
}

.p-2 {
  padding: 8px;
}

.p-3 {
  padding: 12px;
}

.p-4 {
  padding: 16px;
}

.px-1 {
  padding-left: 4px;
  padding-right: 4px;
}

.px-2 {
  padding-left: 8px;
  padding-right: 8px;
}

.px-3 {
  padding-left: 12px;
  padding-right: 12px;
}

.py-1 {
  padding-top: 4px;
  padding-bottom: 4px;
}

.py-2 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.py-3 {
  padding-top: 12px;
  padding-bottom: 12px;
}

.m-1 {
  margin: 4px;
}

.m-2 {
  margin: 8px;
}

.m-3 {
  margin: 12px;
}

.mx-1 {
  margin-left: 4px;
  margin-right: 4px;
}

.mx-2 {
  margin-left: 8px;
  margin-right: 8px;
}

.mx-3 {
  margin-left: 12px;
  margin-right: 12px;
}

.my-1 {
  margin-top: 4px;
  margin-bottom: 4px;
}

.my-2 {
  margin-top: 8px;
  margin-bottom: 8px;
}

.my-3 {
  margin-top: 16px;
  margin-bottom: 16px;
}

.pt-3 {
  padding-top: 12px;
}

.pt-2 {
  padding-top: 8px;
}

.pt-1 {
  padding-top: 4px;
}

.pb-2 {
  padding-bottom: 8px;
}

.justify-end {
  justify-content: flex-end;
}

.w-auto {
  width: auto;
}

.shrink-0 {
  flex-shrink: 0;
}

select.language-select,
select.translate-service,
select.min-select {
  --form-element-spacing-horizontal: 0;
  margin-bottom: 0px;
  max-width: unset;
  flex: 1;
  overflow: hidden;
  font-size: 13px;
  border: none;
  border-radius: 8px;
  padding-right: 30px;
  padding-left: 0px;
  background-position: center right 12px;
  background-size: 16px auto;
  background-image: var(--icon-xia);
  text-overflow: ellipsis;
  color: var(--text-gray-2);
  background-color: transparent;
  box-shadow: unset !important;
  cursor: pointer;
}

select.more {
  background-position: center right;
  padding-right: 20px;
}

select.transform-padding-left {
  padding-left: 12px;
  transform: translateX(-12px);
  background-position: center right 0px;
}

select.translate-service {
  color: var(--text-black-2);
}

/* dark use black, for windows */
@media (prefers-color-scheme: dark) {
  select.language-select option,
  select.translate-service option,
  select.min-select option {
    background-color: #666666;
  }
}

.text-overflow-ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.max-w-20 {
  max-width: 180px;
  white-space: nowrap;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px 0px 20px;
  background-color: var(--popup-header-background-color);
  border-radius: 12px 12px 0px 0px;
}

.popup-header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.popup-header-text {
  font-size: 18px;
  font-weight: 600;
}

.popup-header-button {
  background: none;
  border: none;
  cursor: pointer;
}

.popup-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background-color: var(--popup-footer-background-color);
  border-radius: 0px 0px 12px 12px;
}

.popup-footer-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.popup-footer-text {
  font-size: 14px;
  color: var(--muted-color);
}

.popup-footer-button {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--primary);
  font-size: 14px;
}

.popup-footer-button:hover {
  color: var(--primary-hover);
}

.popup-main {
  padding: 16px 20px;
}

.popup-main-text {
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 16px;
}

.popup-main-language {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.popup-main-language-text {
  font-size: 14px;
  color: var(--muted-color);
}

.popup-main-language-select {
  background: var(--background-color);
  border: 1px solid var(--muted-border-color);
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  color: var(--color);
}

.popup-main-buttons {
  display: flex;
  gap: 8px;
}

.popup-main-button {
  flex: 1;
  padding: 8px 16px;
  border: 1px solid var(--primary);
  border-radius: 4px;
  background: var(--primary);
  color: var(--primary-inverse);
  font-size: 14px;
  cursor: pointer;
}

.popup-main-button:hover {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}

.popup-main-button.secondary {
  background: transparent;
  color: var(--primary);
}

.popup-main-button.secondary:hover {
  background: var(--primary-focus);
}

.popup-close {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  color: var(--muted-color);
}

.popup-close:hover {
  color: var(--color);
}

.popup-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
}

.popup-arrow.top {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 0 8px 8px 8px;
  border-color: transparent transparent var(--muted-border-color) transparent;
}

.popup-arrow.bottom {
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 8px 8px 0 8px;
  border-color: var(--muted-border-color) transparent transparent transparent;
}

.popup-arrow.left {
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-width: 8px 8px 8px 0;
  border-color: transparent var(--muted-border-color) transparent transparent;
}

.popup-arrow.right {
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-width: 8px 0 8px 8px;
  border-color: transparent transparent transparent var(--muted-border-color);
}

@media (max-width: 768px) {
  .popup-container {
    width: 100%;
    min-width: 100%;
  }

  .popup-wrapper {
    margin: 16px;
    max-width: calc(100% - 32px);
  }
}

.custom-select {
  position: relative;
  display: inline-block;
  width: 100%;
}

.custom-select-trigger {
  background: var(--background-color);
  border: 1px solid var(--muted-border-color);
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  color: var(--color);
}

.custom-select-trigger:hover {
  border-color: var(--primary);
}

.custom-select-content {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--background-color);
  border: 1px solid var(--muted-border-color);
  border-radius: 4px;
  box-shadow: var(--service-select-content-shadow);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  display: none;
}

.custom-select.open .custom-select-content {
  display: block;
}

.custom-select-item {
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--color);
  height: 30px;
  line-height: 30px;
}

.custom-select-item-img {
  width: 20px;
  height: 20px;
  margin-right: 4px;
}

@media (prefers-color-scheme: dark) {
  .custom-select-item-img {
    margin-right: 6px;
  }
}

.custom-select-content .custom-select-item.selected,
.custom-select-content .custom-select-item:hover {
  background: var(--service-select-selected-background-color);
}

.custom-select-item > span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-select-item-pro {
  font-size: 12px;
  margin-left: 6px;
}

.custom-select-item-pro img {
  margin: 0 3px;
  width: 20px;
}

.custom-select-group-header {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-gray-9);
  padding: 6px 8px 4px;
  margin-top: 2px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.more-container {
  position: relative;
}

.new-menu-indicator {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #ef3434;
  border-radius: 50%;
  right: 18px;
  top: 4px;
}
`,IMMERSIVE_TRANSLATE_PAGE_POPUP_CSS:`html {
  font-size: 17px;
}

@media print {
  .imt-fb-container {
    display: none !important;
  }
}

#mount#mount {
  position: absolute;
  display: none;
  min-width: 250px;
  height: auto;
  --font-size: 17px;
  font-size: 17px;
}

/* float-ball */
.imt-fb-container {
  position: fixed;
  padding: 0;
  top: 335px;
  width: fit-content;
  display: flex;
  flex-direction: column;
  display: none;
  direction: ltr;
}

.imt-fb-container.left {
  align-items: flex-start;
  left: 0;
}

.imt-fb-container.right {
  align-items: flex-end;
  right: 0;
}

.imt-fb-btn {
  cursor: pointer;
  background: var(--float-ball-more-button-background-color);
  height: 36px;
  width: 56px;
  box-shadow: 2px 6px 10px 0px #0e121629;
}

.imt-fb-btn.left {
  border-top-right-radius: 36px;
  border-bottom-right-radius: 36px;
}

.imt-fb-btn.right {
  border-top-left-radius: 36px;
  border-bottom-left-radius: 36px;
}

.imt-fb-btn div {
  background: var(--float-ball-more-button-background-color);
  height: 36px;
  width: 54px;
  display: flex;
  align-items: center;
}

.imt-fb-btn.left div {
  border-top-right-radius: 34px;
  border-bottom-right-radius: 34px;
  justify-content: flex-end;
}

.imt-fb-btn.right div {
  border-top-left-radius: 34px;
  border-bottom-left-radius: 34px;
}

.imt-fb-logo-img {
  width: 20px;
  height: 20px;
  margin: 0 10px;
}

.imt-fb-logo-img-big-bg {
  width: 28px;
  height: 28px;
  margin: 0;
  padding: 4px;
  background-color: #ed6d8f;
  border-radius: 50%;
  margin: 0 5px;
}

.imt-float-ball-translated {
  position: absolute;
  width: 11px;
  height: 11px;
  bottom: 4px;
  right: 20px;
}

.btn-animate {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  -webkit-transition: -webkit-transform ease-out 250ms;
  transition: -webkit-transform ease-out 250ms;
  transition: transform ease-out 250ms;
  transition: transform ease-out 250ms, -webkit-transform ease-out 250ms;
}

.imt-fb-setting-btn {
  margin-right: 18px;
  width: 28px;
  height: 28px;
}

.immersive-translate-popup-wrapper {
  background: var(--background-color);
  border-radius: 20px;
  box-shadow: 2px 10px 24px 0px #0e121614;
  border: none;
}

.popup-container {
  border-radius: 20px;
}

.popup-content {
  border-radius: 20px 20px 12px 12px;
}
.popup-footer {
  border-radius: 20px;
}

.imt-fb-close-button {
  pointer-events: all;
  cursor: pointer;
  position: absolute;
  margin-top: -10px;
}

.imt-fb-close-button.left {
  right: -10px;
}

.imt-fb-close-button.right {
  left: -10px;
}

.imt-fb-close-button svg {
  width: 20px;
  height: 20px;
  background: var(--float-ball-more-button-background-color);
  border-radius: 50%;
  padding: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.imt-fb-more-btn {
  cursor: pointer;
  background: var(--float-ball-more-button-background-color);
  height: 36px;
  width: 56px;
  box-shadow: 2px 6px 10px 0px #0e121629;
  margin-top: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.imt-fb-more-btn.left {
  border-top-right-radius: 36px;
  border-bottom-right-radius: 36px;
}

.imt-fb-more-btn.right {
  border-top-left-radius: 36px;
  border-bottom-left-radius: 36px;
}

.imt-fb-more-btn svg {
  width: 20px;
  height: 20px;
  color: var(--float-ball-more-button-svg-color);
}

.service-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: hidden;
}

.service-icon img {
  width: 24px;
  height: 24px;
}

.close-icon {
  width: 24px;
  height: 24px;
  cursor: pointer;
  position: absolute;
  right: 0;
  top: 0;
}

/* 底部功能区 */

.side-footer {
  display: flex;
  flex-direction: column;
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: #fff;
}

.side-footer a {
  text-decoration: none;
}

.side-footer-preview {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin: 8px 20px;
}

.upgrade {
  color: #666;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 1.5;
  display: flex;
  align-items: center;
}

.upgrade svg {
  margin-right: 4px;
}

.action-buttons {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.empty-space {
  flex: 1;
}

.side-footer-preview .action-buttons {
  right: 20px;
  bottom: 8px;
}

.action-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  cursor: pointer;
}

.action-icon {
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.reward-center-text {
  font-size: 12px;
  color: #ea4c89;
}

/* 奖励中心弹跳动画 */
@keyframes bounce-reward {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  10% {
    transform: translateY(-8px);
  }
  30% {
    transform: translateY(-6px);
  }
  60% {
    transform: translateY(-4px);
  }
}

.action-icon.bounce-animate {
  animation: bounce-reward 1.2s ease-in-out;
}

.footer-expand {
  display: flex;
  width: 20px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  background: #f3f5f6;
  margin: auto;
  cursor: pointer;
  height: 16px;
  margin-bottom: 4px;
  margin-top: 4px;
}

.footer-area-title {
  margin: 8px 0;
  color: #999;
  font-size: 12px;
  font-weight: 400;
  line-height: 1.5;
}

.footer-area {
  display: flex;
  flex-direction: column;
  margin: 0 16px;
}

.footer-area-buttons-wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
}

.footer-area-button {
  display: flex;
  text-wrap: nowrap;
  padding: 8px;
  align-items: center;
  border-radius: 8px;
  border: 1px solid #ecf0f7;
  cursor: pointer;
  color: #333;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5;
}

.footer-area-button svg {
  margin-right: 4px;
}

/* 错误结果 */
.error-result {
  display: flex;
  padding: 8px 12px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  border-radius: 8px;
  position: relative;
  padding-bottom: 16px;
}

.error-result a {
  color: #ea4c89;
  text-decoration: underline;
}

.error-warning {
  background-color: #feecec;
}

.error-info {
  background-color: #fafbfb;
}

.error-message {
  color: #999;
  text-align: center;
}
`};

// JavaScript logic starts here - this is where the main application code begins
var n0={"translationServices.tencent":"Tencent Translator","translationServices.tenAlpha":"Tencent Translator (Alpha)","translationServices.google":"Google Translate","translationServices.bai":"Baidu (Alpha)","translationServices.baidu":"Baidu Translate","translationServices.aliyun":"Alibaba Translator","translationServices.volc":"Volcano Translation","translationServices.deeplx":"DeepL X","translationServices.bing":"Microsoft Translator","translationServices.deepl":"DeepL","translationServices.wechat":"WeChat translation","translationServices.azure":"Azure Translator","translationServices.ibm":"IBM Watson","translationServices.aws":"Amazon Translate","translationServices.mock":"Mock translation","translationServices.mock2":"Mock translation 2","translationServices.caiyun":"Caiyun Translation","translationServices.cai":"Caiyun Translation (Alpha)","translationServices.volcAlpha":"Volcano Translation (Alpha)","translationServices.openl":"OpenL","translationServices.youdao":"Youdao Translation","translationServices.you":"Youdao Translation (Alpha)","translationServices.transmart":"Tencent Interactive Translation","translationServices.niu":"Xiaoniu Translation","translationServices.papago":"Papago Translation","translationServices.d":"D Translation (Alpha)","translationServices.dpro":"D Pro (Canary)","translationServices.openai":"OpenAI","translationServices.chatgpt":"ChatGPT Web(3.5 mobile)","translationServices.yandex":"Yandex Translate","translationServices.claude":"Claude","translationServices.gemini":"Gemini","translationServices.custom":"Custom translation","translationServices.openrouter":"OpenRouter","translationServices.zhipu":"Zhipu BigModel","translationServices.lingyiwanwu":"01.AI","translationServices.grok":"Grok","translationServices.ollama":"Ollama","translationServices.groq":"Groq","translationServices.azure-openai":"Azure OpenAI","translationServices.hunyuan":"Tencent Hunyuan","translationServices.qianfan":"Baidu Qianfan","translationServices.qianfan2":"Baidu Qianfan","translationServices.aliyun-bailian":"Alibaba Cloud","translationServices.deepseek":"DeepSeek","translationServices.doubao":"Volcengine","translationServices.siliconcloud":"SiliconFlow","translationServices.zhipu-pro":"Zhipu 4 Plus","popup.translateLocalPdfFile":"Translate local PDF file","popup.translateLocalHtmlFile":"Translate HTML/txt file","popup.translateLocalSubtitleFile":"Translate subtitle file","popup.translateToThePageEndImmediately":"Translate to the bottom of the page immediately (instead of waiting for scrolling)","popup.translateTheMainPage":"Translate main page","popup.translateToThePageEndImmediatelyDescription":"After opening, the entire page will be translated immediately after opening, instead of translating while scrolling. (Not recommended to open)","popup.tabOnlyTranslateTheCurrentTab":"Only translate the current tab","popup.tabOnlyTranslateTheCurrentTabDescription":"After opening, only the current tab will be translated, not all tabs","popup.translateTheWholePage":"Translate the entire page area (different from only translating the main area)","popup.translateTheWholePageDescription":"After opening, the entire page area will be translated, not just the main area","popup.translateToThePageEndImmediatelyDescription2":"After opening, the entire page will be translated immediately after opening, instead of translating while scrolling. (Not recommended to open)","popup.translateTheMainPageDescription":"Use the smart algorithm to translate only the main content area, excluding the header, navigation, sidebar, footer and other areas","popup.translateTheWholePageDescription2":"After opening, the entire page area will be translated, not just the main area","popup.moreOptions":"More options","popup.translateTheWholePage2":"Translate the entire page area","popup.translateTheMainPage2":"Smart translation of main content","popup.translateToThePageEndImmediately2":"Translate immediately","popup.translateTheCurrentParagraphImmediately":"Translate the current paragraph immediately","popup.translateTheCurrentParagraphImmediatelyDescription":"After opening, the paragraph where the mouse is located will be translated immediately, instead of clicking the shortcut key to translate","popup.translateTheCurrentParagraphImmediatelyDescription2":"After opening, the paragraph where the mouse is located will be translated immediately, instead of clicking the shortcut key to translate","popup.translateTheCurrentParagraphImmediately2":"Translate the current paragraph immediately","popup.translateTheCurrentParagraphImmediatelyDescription3":"After opening, the paragraph where the mouse is located will be translated immediately, instead of clicking the shortcut key to translate","popup.translateTheCurrentParagraphImmediately3":"Translate the current paragraph immediately","popup.translateTheCurrentParagraphImmediatelyDescription4":"After opening, the paragraph where the mouse is located will be translated immediately, instead of clicking the shortcut key to translate","popup.translateTheCurrentParagraphImmediately4":"Translate the current paragraph immediately","popup.translateTheCurrentParagraphImmediatelyDescription5":"After opening, the paragraph where the mouse is located will be translated immediately, instead of clicking the shortcut key to translate","popup.translateTheCurrentParagraphImmediately5":"Translate the current paragraph immediately","popup.translateTheCurrentParagraphImmediatelyDescription6":"After opening, the paragraph where the mouse is located will be translated immediately, instead of clicking the shortcut key to translate","popup.translateTheCurrentParagraphImmediately6":"Translate the current paragraph immediately","popup.translateTheCurrentParagraphImmediatelyDescription7":"After opening, the paragraph where the mouse is located will be translated immediately, instead of clicking the shortcut key to translate","popup.translateTheCurrentParagraphImmediately7":"Translate the current paragraph immediately","popup.translateTheCurrentParagraphImmediatelyDescription8":"After opening, the paragraph where the mouse is located will be translated immediately, instead of clicking the shortcut key to translate","popup.translateTheCurrentParagraphImmediately8":"Translate the current paragraph immediately","popup.translateTheCurrentParagraphImmediatelyDescription9":"After opening, the paragraph where the mouse is located will be translated immediately, instead of clicking the shortcut key to translate","popup.translateTheCurrentParagraphImmediately9":"Translate the current paragraph immediately","popup.translateTheCurrentParagraphImmediatelyDescription10":"After opening, the paragraph where the mouse is located will be translated immediately, instead of clicking the shortcut key to translate","popup.translateTheCurrentParagraphImmediately10":"Translate the current paragraph immediately"};

// More JavaScript variables and functions continue here...
var r0={"translationServices.tencent":"騰訊翻譯君","translationServices.tenAlpha":"騰訊翻譯君(Alpha)","translationServices.google":"Google 翻譯","translationServices.bai":"百度(Alpha)","translationServices.baidu":"百度翻譯","translationServices.aliyun":"阿里雲翻譯","translationServices.volc":"火山翻譯","translationServices.deeplx":"DeepL X","translationServices.bing":"微軟翻譯","translationServices.deepl":"DeepL","translationServices.wechat":"微信翻譯","translationServices.azure":"Azure 翻譯","translationServices.ibm":"IBM Watson","translationServices.aws":"亞馬遜翻譯","translationServices.mock":"模擬翻譯","translationServices.mock2":"模擬翻譯2","translationServices.caiyun":"彩雲小譯","translationServices.cai":"彩雲小譯 (Alpha)","translationServices.volcAlpha":"火山翻譯(Alpha)","translationServices.openl":"OpenL","translationServices.youdao":"有道翻譯","translationServices.you":"有道翻譯 (Alpha)","translationServices.transmart":"騰訊交互翻譯","translationServices.niu":"小牛翻譯","translationServices.papago":"Papago翻譯","translationServices.d":"D翻譯(Alpha)","translationServices.dpro":"D Pro (Canary)","translationServices.openai":"OpenAI","translationServices.chatgpt":"ChatGPT網頁版(3.5 mobile)","translationServices.yandex":"Yandex翻譯","translationServices.claude":"Claude","translationServices.gemini":"Gemini","translationServices.custom":"自定義翻譯","translationServices.openrouter":"OpenRouter","translationServices.zhipu":"智譜清言","translationServices.lingyiwanwu":"零一萬物","translationServices.grok":"Grok","translationServices.ollama":"Ollama","translationServices.groq":"Groq","translationServices.azure-openai":"Azure OpenAI","translationServices.hunyuan":"騰訊混元","translationServices.qianfan":"百度千帆","translationServices.qianfan2":"百度千帆","translationServices.aliyun-bailian":"阿里雲百煉","translationServices.deepseek":"DeepSeek","translationServices.doubao":"火山方舟","translationServices.siliconcloud":"SiliconFlow","translationServices.zhipu-pro":"智譜4 Plus"};

// Continue with more JavaScript logic and variables...
var i0={"translationServices.tencent":"Tencent Translator","translationServices.tenAlpha":"Tencent Translator (Alpha)","translationServices.google":"Google Translate","translationServices.bai":"Baidu (Alpha)","translationServices.baidu":"Baidu Translate","translationServices.aliyun":"Alibaba Translator","translationServices.volc":"Volcano Translation","translationServices.deeplx":"DeepL X","translationServices.bing":"Microsoft Translator","translationServices.deepl":"DeepL","translationServices.wechat":"WeChat translation","translationServices.azure":"Azure Translator","translationServices.ibm":"IBM Watson","translationServices.aws":"Amazon Translate","translationServices.mock":"Mock translation","translationServices.mock2":"Mock translation 2","translationServices.caiyun":"Caiyun Translation","translationServices.cai":"Caiyun Translation (Alpha)","translationServices.volcAlpha":"Volcano Translation (Alpha)","translationServices.openl":"OpenL","translationServices.youdao":"Youdao Translation","translationServices.you":"Youdao Translation (Alpha)","translationServices.transmart":"Tencent Interactive Translation","translationServices.niu":"Xiaoniu Translation","translationServices.papago":"Papago Translation","translationServices.d":"D Translation (Alpha)","translationServices.dpro":"D Pro (Canary)","translationServices.openai":"OpenAI","translationServices.chatgpt":"ChatGPT Web(3.5 mobile)","translationServices.yandex":"Yandex Translate","translationServices.claude":"Claude","translationServices.gemini":"Gemini","translationServices.custom":"Custom translation","translationServices.openrouter":"OpenRouter","translationServices.zhipu":"Zhipu BigModel","translationServices.lingyiwanwu":"01.AI","translationServices.grok":"Grok","translationServices.ollama":"Ollama","translationServices.groq":"Groq","translationServices.azure-openai":"Azure OpenAI","translationServices.hunyuan":"Tencent Hunyuan","translationServices.qianfan":"Baidu Qianfan","translationServices.qianfan2":"Baidu Qianfan","translationServices.aliyun-bailian":"Alibaba Cloud","translationServices.deepseek":"DeepSeek","translationServices.doubao":"Volcengine","translationServices.siliconcloud":"SiliconFlow","translationServices.zhipu-pro":"Zhipu 4 Plus"};

// Main application logic continues...
var E3=[{code:"zh-CN",messages:n0},{code:"zh-TW",messages:r0},{code:"en",messages:i0}];
var o0=["zh-CN","zh-TW","en","ja","ar","de","es","fa","fr","he","hi","hu","it","ru","ko","pt-PT","pt-BR","tr"];
var Fa={};
for(let e of E3)Fa[e.code]=e.messages;
