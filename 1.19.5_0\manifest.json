{"action": {"default_icon": {"128": "icons/128.png", "256": "icons/256.png", "32": "icons/32.png", "48": "icons/48.png", "64": "icons/64.png"}, "default_popup": "popup.html"}, "background": {"service_worker": "background.js"}, "commands": {"shareToDraft": {"description": "__MSG_shareToDraft__"}, "toggleMouseHoverTranslateDirectly": {"description": "__MSG_toggleMouseHoverTranslateDirectly__"}, "toggleOnlyTransation": {"description": "__MSG_toggleOnlyTransation__"}, "toggleSidePanel": {"description": "__MSG_toggleSidePanel__", "suggested_key": {"default": "Alt+S"}}, "toggleTranslatePage": {"description": "__MSG_toggleTranslatePageOfficialPage__", "suggested_key": {"default": "Alt+A"}}, "toggleTranslateTheMainPage": {"description": "__MSG_toggleTranslateTheMainPage__"}, "toggleTranslateTheWholePage": {"description": "__MSG_toggleTranslateTheWholePage__", "suggested_key": {"default": "Alt+W"}}, "toggleTranslateToThePageEndImmediately": {"description": "__MSG_toggleTranslateToThePageEndImmediately__"}, "toggleTranslationMask": {"description": "__MSG_toggleTranslationMask__"}, "toggleVideoSubtitlePreTranslation": {"description": "__MSG_toggleVideoSubtitlePreTranslation__"}, "translateInputBox": {"description": "__MSG_translateInputBox__"}, "translateWithBing": {"description": "__MSG_translateWithBing__"}, "translateWithClaude": {"description": "__MSG_translateWithClaude__"}, "translateWithCustom1": {"description": "__MSG_translateWithCustom1__"}, "translateWithCustom2": {"description": "__MSG_translateWithCustom2__"}, "translateWithCustom3": {"description": "__MSG_translateWithCustom3__"}, "translateWithDeepL": {"description": "__MSG_translateWithDeepL__"}, "translateWithGemini": {"description": "__MSG_translateWithGemini__"}, "translateWithGoogle": {"description": "__MSG_translateWithGoogle__"}, "translateWithOpenAI": {"description": "__MSG_translateWithOpenAI__"}, "translateWithTransmart": {"description": "__MSG_translateWithTransmart__"}}, "content_scripts": [{"all_frames": true, "js": ["content_script.js"], "matches": ["<all_urls>", "file:///*", "*://*/*"], "run_at": "document_start"}, {"all_frames": true, "js": ["content_start.js"], "matches": ["<all_urls>", "file:///*", "*://*/*"], "run_at": "document_start"}], "content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'self'"}, "declarative_net_request": {"rule_resources": [{"enabled": true, "id": "ruleset_1", "path": "rules/request_modifier_rule.json"}]}, "default_locale": "en", "description": "__MSG_brandDescription__", "host_permissions": ["<all_urls>"], "icons": {"128": "icons/128.png", "256": "icons/256.png", "32": "icons/32.png", "48": "icons/48.png", "64": "icons/64.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA7JPn78UfqI3xIIOPPLPS74UTzLfJL1gQM8hlk/deKWvFP/WqUBnPJPdhQeF45sFpI1OjO70nFqdATT4/RwYAiZK7G/E6m27MDVnhHjszfzReOuoAEn9J3RnE2xEx5pFhRFcelhnwTTLrrn90aaPcaMtNsgXtZA1Ggz/SnX9I4ZygqpJYjx3Ql2t6SyNK222oRQiKMT93Rrjgyc8RFA7FKXsWglG0TvseRjbmG5Jk5gDx+2/YTcWGqCDotQnWnkPj/dBO23UAX7IpyJK3FGYdkvWFih6OVClHIIWY8mfCjjwSGbXNQNesaa9F2hrzBZ5MRTj4m7yj76mGxuPHPIE8mwIDAQAB", "manifest_version": 3, "name": "__MSG_brandName__", "options_page": "options.html", "permissions": ["storage", "activeTab", "contextMenus", "webRequest", "declarativeNetRequestWithHostAccess", "declarativeNetRequestFeedback", "declarativeNetRequest", "offscreen", "sidePanel"], "update_url": "https://clients2.google.com/service/update2/crx", "version": "1.19.5", "web_accessible_resources": [{"matches": ["<all_urls>", "file:///*", "*://*/*"], "resources": ["styles/inject.css", "pdf/index.html", "video-subtitle/inject.js", "image/inject.js", "tesseract/worker.min.js", "tesseract/tesseract-core-simd-lstm.wasm.js", "browser-bridge/inject.js", "side-panel.html"]}]}