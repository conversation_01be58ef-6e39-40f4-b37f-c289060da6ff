.imt-image-tools {
  display: flex;
  position: fixed;
  flex-direction: row;
  align-items: center;
  z-index: 2147483647;
}

.imt-image-translate-button {
  width: 30px;
  height: 30px;
  flex-shrink: 0;
  background-color: #00000099;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 15px;
  cursor: pointer;
}

.imt-fb-logo-img {
  width: 14px;
  height: 14px;
  margin: 0;
}

.close-button {
  position: absolute;
  top: -18px;
  right: -18px;
  width: 24px;
  height: 24px;
  padding: 5px;
  line-height: 0;
  display: none;
  cursor: pointer;
}

.imt-image-tools:hover .close-button {
  display: block;
}

.translated-icon {
  position: absolute;
  right: 4px;
  bottom: 4px;
  width: 8px;
  height: 8px;
}

.immersive-translate-loading-spinner {
  vertical-align: middle !important;
  width: 10px !important;
  height: 10px !important;
  display: inline-block !important;
  margin: 0 4px !important;
  border: 2px rgba(221, 244, 255, 0.6) solid !important;
  border-top: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-left: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-radius: 50% !important;
  padding: 0 !important;
  -webkit-animation: immersive-translate-loading-animation 0.6s infinite linear !important;
  animation: immersive-translate-loading-animation 0.6s infinite linear !important;
}

@-webkit-keyframes immersive-translate-loading-animation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
  }
}

@keyframes immersive-translate-loading-animation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}
